"""
Exception classes for the pheromone maze navigation system.
"""


class NavigationException(Exception):
    """Base exception for navigation-related errors."""
    pass


class MazeGenerationException(NavigationException):
    """Exception raised when maze generation fails."""
    
    def __init__(self, message: str = "Failed to generate valid maze"):
        super().__init__(message)
        self.message = message


class PheromoneOverflowException(NavigationException):
    """Exception raised when pheromone values overflow or become invalid."""
    
    def __init__(self, message: str = "Pheromone field values have overflowed"):
        super().__init__(message)
        self.message = message


class InvalidPositionException(NavigationException):
    """Exception raised when trying to access invalid positions."""
    
    def __init__(self, x: int, y: int, message: str = None):
        if message is None:
            message = f"Invalid position: ({x}, {y})"
        super().__init__(message)
        self.x = x
        self.y = y
        self.message = message


class AgentStuckException(NavigationException):
    """Exception raised when agent cannot make progress."""
    
    def __init__(self, steps: int, message: str = None):
        if message is None:
            message = f"Agent stuck after {steps} steps"
        super().__init__(message)
        self.steps = steps
        self.message = message