"""
Pheromone field management module for the pheromone-based navigation system.

This module manages the dual pheromone system:
- Obstacle pheromones (negative values) released when agents hit obstacles
- Target pheromones (positive values) continuously emitted by the target location
"""

import numpy as np
from typing import Dict, <PERSON>ple, Optional
from scipy import ndimage
from .config import SimulationConfig, PheromoneData
from .exceptions import PheromoneOverflowException, InvalidPositionException
from .utils import is_valid_coordinate, get_direction_offset, Direction


class PheromoneField:
    """
    Manages the pheromone field system for agent navigation.
    
    The pheromone field consists of two separate arrays:
    - obstacle_field: Contains negative pheromone values from obstacle collisions
    - target_field: Contains positive pheromone values from the target location
    
    These fields are combined to create directional gradients that guide agent movement.
    """
    
    def __init__(self, width: int, height: int, config: Optional[SimulationConfig] = None):
        """
        Initialize the pheromone field with given dimensions.
        
        Args:
            width: Width of the pheromone field
            height: Height of the pheromone field
            config: Configuration object with pheromone parameters
            
        Raises:
            ValueError: If dimensions are invalid
        """
        if width <= 0 or height <= 0:
            raise ValueError("Pheromone field dimensions must be positive")
        
        self.width = width
        self.height = height
        self.config = config or SimulationConfig()
        
        # Initialize pheromone arrays
        self.obstacle_field = np.zeros((height, width), dtype=np.float64)
        self.target_field = np.zeros((height, width), dtype=np.float64)
        self.combined_field = np.zeros((height, width), dtype=np.float64)
        
        # Target position for continuous pheromone generation
        self.target_position: Optional[Tuple[int, int]] = None
        
        # Track field statistics
        self.total_obstacle_pheromone_added = 0.0
        self.total_target_pheromone_added = 0.0
        self.update_count = 0
        
        # Diffusion kernel for pheromone spreading
        self.diffusion_kernel = self._create_diffusion_kernel()
        
        # Numerical limits for overflow protection
        self.max_pheromone_value = 1e6
        self.min_pheromone_value = -1e6
    
    def add_obstacle_pheromone(self, x: int, y: int, intensity: Optional[float] = None) -> None:
        """
        Add obstacle pheromone at the specified position.
        
        Args:
            x: X coordinate
            y: Y coordinate
            intensity: Pheromone intensity (uses config default if None)
            
        Raises:
            InvalidPositionException: If position is out of bounds
            PheromoneOverflowException: If pheromone values become invalid
        """
        if not self._is_valid_position(x, y):
            raise InvalidPositionException(x, y)
        
        if intensity is None:
            intensity = self.config.obstacle_pheromone_intensity
        
        # Add pheromone (obstacle pheromones are typically negative)
        old_value = self.obstacle_field[y, x]
        new_value = old_value + intensity
        
        # Check for overflow/underflow
        if not np.isfinite(new_value):
            raise PheromoneOverflowException(
                f"Pheromone overflow at position ({x}, {y}): {old_value} + {intensity} = {new_value}"
            )
        
        self.obstacle_field[y, x] = new_value
        self.total_obstacle_pheromone_added += intensity
        
        # Update combined field
        self._update_combined_field_at(x, y)
    
    def add_target_pheromone(self, x: int, y: int, intensity: Optional[float] = None) -> None:
        """
        Add target pheromone at the specified position.
        
        Args:
            x: X coordinate
            y: Y coordinate
            intensity: Pheromone intensity (uses config default if None)
            
        Raises:
            InvalidPositionException: If position is out of bounds
            PheromoneOverflowException: If pheromone values become invalid
        """
        if not self._is_valid_position(x, y):
            raise InvalidPositionException(x, y)
        
        if intensity is None:
            intensity = self.config.target_pheromone_intensity
        
        # Add pheromone (target pheromones are typically positive)
        old_value = self.target_field[y, x]
        new_value = old_value + intensity
        
        # Check for overflow/underflow
        if not np.isfinite(new_value):
            raise PheromoneOverflowException(
                f"Pheromone overflow at position ({x}, {y}): {old_value} + {intensity} = {new_value}"
            )
        
        self.target_field[y, x] = new_value
        self.total_target_pheromone_added += intensity
        
        # Update combined field
        self._update_combined_field_at(x, y)
    
    def get_obstacle_pheromone(self, x: int, y: int) -> float:
        """
        Get obstacle pheromone intensity at the specified position.
        
        Args:
            x: X coordinate
            y: Y coordinate
            
        Returns:
            Obstacle pheromone intensity
            
        Raises:
            InvalidPositionException: If position is out of bounds
        """
        if not self._is_valid_position(x, y):
            raise InvalidPositionException(x, y)
        
        return float(self.obstacle_field[y, x])
    
    def get_target_pheromone(self, x: int, y: int) -> float:
        """
        Get target pheromone intensity at the specified position.
        
        Args:
            x: X coordinate
            y: Y coordinate
            
        Returns:
            Target pheromone intensity
            
        Raises:
            InvalidPositionException: If position is out of bounds
        """
        if not self._is_valid_position(x, y):
            raise InvalidPositionException(x, y)
        
        return float(self.target_field[y, x])
    
    def get_combined_pheromone(self, x: int, y: int) -> float:
        """
        Get combined pheromone intensity at the specified position.
        
        Args:
            x: X coordinate
            y: Y coordinate
            
        Returns:
            Combined pheromone intensity (obstacle + target)
            
        Raises:
            InvalidPositionException: If position is out of bounds
        """
        if not self._is_valid_position(x, y):
            raise InvalidPositionException(x, y)
        
        return float(self.combined_field[y, x])
    
    def get_pheromone_data(self) -> PheromoneData:
        """
        Get a copy of all pheromone field data.
        
        Returns:
            PheromoneData object containing copies of all fields
        """
        return PheromoneData(
            obstacle_field=self.obstacle_field.copy(),
            target_field=self.target_field.copy(),
            combined_field=self.combined_field.copy()
        )
    
    def clear_obstacle_pheromones(self) -> None:
        """Clear all obstacle pheromones from the field."""
        self.obstacle_field.fill(0.0)
        self.total_obstacle_pheromone_added = 0.0
        self._update_combined_field()
    
    def clear_target_pheromones(self) -> None:
        """Clear all target pheromones from the field."""
        self.target_field.fill(0.0)
        self.total_target_pheromone_added = 0.0
        self._update_combined_field()
    
    def clear_all_pheromones(self) -> None:
        """Clear all pheromones from the field."""
        self.obstacle_field.fill(0.0)
        self.target_field.fill(0.0)
        self.combined_field.fill(0.0)
        self.total_obstacle_pheromone_added = 0.0
        self.total_target_pheromone_added = 0.0
        self.update_count = 0
    
    def set_target_position(self, x: int, y: int) -> None:
        """
        Set the target position for continuous pheromone generation.
        
        Args:
            x: X coordinate of target
            y: Y coordinate of target
            
        Raises:
            InvalidPositionException: If position is out of bounds
        """
        if not self._is_valid_position(x, y):
            raise InvalidPositionException(x, y)
        
        self.target_position = (x, y)
        # Initialize target pheromone field immediately
        self.generate_target_pheromone_field()
    
    def generate_target_pheromone_field(self) -> None:
        """
        Generate the complete target pheromone field based on distance from target.
        
        This method creates a distance-based pheromone field where intensity
        decreases with distance from the target position. The pheromone values
        are positive (opposite sign to obstacle pheromones).
        
        Raises:
            ValueError: If target position is not set
        """
        if self.target_position is None:
            raise ValueError("Target position must be set before generating target pheromone field")
        
        target_x, target_y = self.target_position
        base_intensity = self.config.target_pheromone_intensity
        
        # Clear existing target field
        self.target_field.fill(0.0)
        
        # Generate distance-based pheromone field
        for y in range(self.height):
            for x in range(self.width):
                # Calculate Euclidean distance from target
                distance = np.sqrt((x - target_x)**2 + (y - target_y)**2)
                
                # Apply distance-based decay formula: intensity / (1 + distance)
                # This ensures maximum intensity at target and gradual decay
                if distance == 0:
                    # Target position gets full intensity
                    pheromone_intensity = base_intensity
                else:
                    # Distance-based decay with smooth falloff
                    pheromone_intensity = base_intensity / (1 + distance)
                
                self.target_field[y, x] = pheromone_intensity
        
        # Update combined field
        self._update_combined_field()
        
        # Track total pheromone added
        self.total_target_pheromone_added += np.sum(self.target_field)
    
    def update_target_pheromones(self) -> None:
        """
        Update target pheromones by regenerating the field.
        
        This method ensures continuous emission of target pheromones by
        regenerating the distance-based field each update cycle.
        """
        if self.target_position is not None:
            # Store current total for tracking
            old_total = np.sum(self.target_field)
            
            # Regenerate target pheromone field
            self.generate_target_pheromone_field()
            
            # Update tracking (subtract old, add new is handled in generate method)
            new_total = np.sum(self.target_field)
            self.total_target_pheromone_added = self.total_target_pheromone_added - old_total + new_total
    
    def update_field(self) -> None:
        """
        Update the pheromone field by applying diffusion and decay.
        
        This method should be called each simulation step to evolve the pheromone field.
        Target pheromones are continuously regenerated to maintain attraction.
        
        Raises:
            PheromoneOverflowException: If pheromone values become invalid during update
        """
        try:
            # Apply decay to obstacle pheromones only
            # Target pheromones are regenerated, not decayed
            self.decay_obstacle_pheromones()
            
            # Regenerate target pheromones (continuous emission)
            self.update_target_pheromones()
            
            # Apply diffusion to both fields
            self.diffuse_pheromones()
            
            # Update combined field
            self._update_combined_field()
            
            # Check for numerical issues
            self._check_field_validity()
            
        except Exception as e:
            raise PheromoneOverflowException(f"Error during field update: {str(e)}")
    
    def diffuse_pheromones(self) -> None:
        """
        Apply Gaussian kernel-based diffusion to pheromone fields.
        
        Pheromones spread to neighboring cells according to the diffusion rate.
        Uses scipy.ndimage.convolve for efficient convolution with boundary handling.
        
        Note: Target pheromones are regenerated each update, so diffusion only
        affects obstacle pheromones to maintain target field integrity.
        
        Raises:
            PheromoneOverflowException: If diffusion causes numerical overflow
        """
        try:
            # Apply diffusion to obstacle field only
            # Target field is regenerated each update, so no diffusion needed
            if np.any(self.obstacle_field != 0):
                diffused_obstacle = ndimage.convolve(
                    self.obstacle_field, 
                    self.diffusion_kernel, 
                    mode='constant', 
                    cval=0.0
                )
                
                # Blend with original field based on diffusion rate
                self.obstacle_field = (
                    (1 - self.config.pheromone_diffusion_rate) * self.obstacle_field +
                    self.config.pheromone_diffusion_rate * diffused_obstacle
                )
            
            # Apply numerical limits to prevent overflow
            self._apply_numerical_limits()
            
        except Exception as e:
            raise PheromoneOverflowException(f"Error during pheromone diffusion: {str(e)}")
    
    def decay_pheromones(self) -> None:
        """
        Apply time-based decay to all pheromone fields.
        
        Pheromone intensities are multiplied by the decay rate each time step,
        causing them to gradually weaken over time.
        """
        # Apply decay to obstacle field
        self.obstacle_field *= self.config.pheromone_decay_rate
        
        # Apply decay to target field
        self.target_field *= self.config.pheromone_decay_rate
        
        # Remove very small values to prevent numerical drift
        self._remove_negligible_values()
    
    def decay_obstacle_pheromones(self) -> None:
        """
        Apply time-based decay to obstacle pheromones only.
        
        Target pheromones are not decayed as they are continuously regenerated.
        """
        # Apply decay to obstacle field only
        self.obstacle_field *= self.config.pheromone_decay_rate
        
        # Remove very small values from obstacle field
        threshold = 1e-10
        self.obstacle_field[np.abs(self.obstacle_field) < threshold] = 0.0
    
    def get_directional_pheromone_strength(self, x: int, y: int, direction: str, maze=None) -> float:
        """
        Get pheromone strength in a specific direction from a position.
        
        This method calculates the combined pheromone intensity (obstacle + target)
        in the specified direction, with special handling for walls.
        
        Args:
            x: Current x coordinate
            y: Current y coordinate
            direction: Direction to sense ('w', 's', 'a', 'd')
            maze: Optional maze object for wall detection
            
        Returns:
            Combined pheromone strength in the specified direction
            
        Raises:
            InvalidPositionException: If position is out of bounds
            ValueError: If direction is invalid
        """
        if not self._is_valid_position(x, y):
            raise InvalidPositionException(x, y)
        
        # Get target position in the specified direction
        from .utils import get_new_position
        target_x, target_y = get_new_position(x, y, direction)
        
        # Check if target position is out of bounds
        if not self._is_valid_position(target_x, target_y):
            return float('-inf')  # Out of bounds treated as wall
        
        # Check if target position is a wall (if maze is provided)
        if maze is not None:
            try:
                if maze.is_obstacle(target_x, target_y):
                    return float('-inf')  # Wall positions have negative infinity
            except:
                # If maze check fails, continue with normal calculation
                pass
        
        # Get combined pheromone strength at target position
        return self.get_combined_pheromone(target_x, target_y)
    
    def get_all_directional_strengths(self, x: int, y: int, maze=None) -> Dict[str, float]:
        """
        Get pheromone strengths in all four directions from a position.
        
        Args:
            x: Current x coordinate
            y: Current y coordinate
            maze: Optional maze object for wall detection
            
        Returns:
            Dictionary mapping directions ('w', 's', 'a', 'd') to pheromone strengths
            
        Raises:
            InvalidPositionException: If position is out of bounds
        """
        if not self._is_valid_position(x, y):
            raise InvalidPositionException(x, y)
        
        from .utils import Direction
        
        strengths = {}
        for direction in Direction.all_directions():
            strengths[direction] = self.get_directional_pheromone_strength(x, y, direction, maze)
        
        return strengths
    
    def calculate_pheromone_gradient(self, x: int, y: int, maze=None) -> Dict[str, float]:
        """
        Calculate pheromone gradient in all directions from a position.
        
        The gradient represents the difference between the current position's
        pheromone strength and each neighboring position.
        
        Args:
            x: Current x coordinate
            y: Current y coordinate
            maze: Optional maze object for wall detection
            
        Returns:
            Dictionary mapping directions to gradient values (neighbor - current)
            
        Raises:
            InvalidPositionException: If position is out of bounds
        """
        if not self._is_valid_position(x, y):
            raise InvalidPositionException(x, y)
        
        current_strength = self.get_combined_pheromone(x, y)
        directional_strengths = self.get_all_directional_strengths(x, y, maze)
        
        gradients = {}
        for direction, neighbor_strength in directional_strengths.items():
            if neighbor_strength == float('-inf'):
                gradients[direction] = float('-inf')  # Wall gradient
            else:
                gradients[direction] = neighbor_strength - current_strength
        
        return gradients
    
    def get_best_direction(self, x: int, y: int, maze=None) -> str:
        """
        Get the direction with the highest pheromone strength.
        
        Args:
            x: Current x coordinate
            y: Current y coordinate
            maze: Optional maze object for wall detection
            
        Returns:
            Direction string ('w', 's', 'a', 'd') with highest pheromone strength
            
        Raises:
            InvalidPositionException: If position is out of bounds
        """
        strengths = self.get_all_directional_strengths(x, y, maze)
        
        # Filter out wall directions (negative infinity)
        valid_directions = {d: s for d, s in strengths.items() if s != float('-inf')}
        
        if not valid_directions:
            # All directions are walls, return arbitrary direction
            return 'w'
        
        # Return direction with maximum strength
        return max(valid_directions, key=valid_directions.get)
    
    def get_field_statistics(self) -> Dict[str, float]:
        """
        Get statistics about the pheromone field.
        
        Returns:
            Dictionary containing field statistics
        """
        return {
            'obstacle_min': float(np.min(self.obstacle_field)),
            'obstacle_max': float(np.max(self.obstacle_field)),
            'obstacle_mean': float(np.mean(self.obstacle_field)),
            'obstacle_std': float(np.std(self.obstacle_field)),
            'target_min': float(np.min(self.target_field)),
            'target_max': float(np.max(self.target_field)),
            'target_mean': float(np.mean(self.target_field)),
            'target_std': float(np.std(self.target_field)),
            'combined_min': float(np.min(self.combined_field)),
            'combined_max': float(np.max(self.combined_field)),
            'combined_mean': float(np.mean(self.combined_field)),
            'combined_std': float(np.std(self.combined_field)),
            'total_obstacle_added': self.total_obstacle_pheromone_added,
            'total_target_added': self.total_target_pheromone_added,
            'update_count': self.update_count
        }
    
    def _is_valid_position(self, x: int, y: int) -> bool:
        """Check if position is valid within field bounds."""
        return is_valid_coordinate(x, y, self.width, self.height)
    
    def _update_combined_field_at(self, x: int, y: int) -> None:
        """Update combined field at a specific position."""
        self.combined_field[y, x] = self.obstacle_field[y, x] + self.target_field[y, x]
    
    def _update_combined_field(self) -> None:
        """Update the entire combined field."""
        self.combined_field = self.obstacle_field + self.target_field
        self.update_count += 1
    
    def _create_diffusion_kernel(self) -> np.ndarray:
        """
        Create a Gaussian diffusion kernel for pheromone spreading.
        
        Returns:
            3x3 normalized Gaussian kernel
        """
        # Create a 3x3 Gaussian kernel
        kernel = np.array([
            [0.05, 0.1, 0.05],
            [0.1,  0.4, 0.1],
            [0.05, 0.1, 0.05]
        ], dtype=np.float64)
        
        # Normalize to ensure conservation of total pheromone
        return kernel / np.sum(kernel)
    
    def _apply_numerical_limits(self) -> None:
        """Apply numerical limits to prevent overflow/underflow."""
        # Clip obstacle field
        self.obstacle_field = np.clip(
            self.obstacle_field, 
            self.min_pheromone_value, 
            self.max_pheromone_value
        )
        
        # Clip target field
        self.target_field = np.clip(
            self.target_field, 
            self.min_pheromone_value, 
            self.max_pheromone_value
        )
    
    def _remove_negligible_values(self) -> None:
        """Remove very small values to prevent numerical drift."""
        threshold = 1e-10
        
        # Set very small values to zero
        self.obstacle_field[np.abs(self.obstacle_field) < threshold] = 0.0
        self.target_field[np.abs(self.target_field) < threshold] = 0.0
    
    def _check_field_validity(self) -> None:
        """
        Check if pheromone fields contain valid values.
        
        Raises:
            PheromoneOverflowException: If fields contain invalid values
        """
        # Check for NaN or infinite values
        if not np.all(np.isfinite(self.obstacle_field)):
            raise PheromoneOverflowException("Obstacle field contains NaN or infinite values")
        
        if not np.all(np.isfinite(self.target_field)):
            raise PheromoneOverflowException("Target field contains NaN or infinite values")
        
        if not np.all(np.isfinite(self.combined_field)):
            raise PheromoneOverflowException("Combined field contains NaN or infinite values")
    
    def __str__(self) -> str:
        """String representation of the pheromone field for debugging."""
        stats = self.get_field_statistics()
        return (
            f"PheromoneField({self.width}x{self.height})\n"
            f"Obstacle: min={stats['obstacle_min']:.2f}, max={stats['obstacle_max']:.2f}, "
            f"mean={stats['obstacle_mean']:.2f}\n"
            f"Target: min={stats['target_min']:.2f}, max={stats['target_max']:.2f}, "
            f"mean={stats['target_mean']:.2f}\n"
            f"Combined: min={stats['combined_min']:.2f}, max={stats['combined_max']:.2f}, "
            f"mean={stats['combined_mean']:.2f}"
        )