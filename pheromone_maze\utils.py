"""
Utility functions for coordinate system and direction mapping.
"""

from enum import Enum
from typing import Tuple, Dict
import numpy as np


class Direction(Enum):
    """Enumeration for movement directions."""
    UP = 'w'
    DOWN = 's'
    LEFT = 'a'
    RIGHT = 'd'
    
    @classmethod
    def all_directions(cls):
        """Get all direction values."""
        return [d.value for d in cls]
    
    @classmethod
    def from_string(cls, direction_str: str):
        """Create Direction from string."""
        for direction in cls:
            if direction.value == direction_str.lower():
                return direction
        raise ValueError(f"Invalid direction: {direction_str}")


# Direction offset mapping for coordinate system
# Using standard Cartesian coordinates: (0,0) at top-left, x-right, y-down
DIRECTION_OFFSETS: Dict[str, Tuple[int, int]] = {
    'w': (0, -1),   # Up: decrease y
    's': (0, 1),    # Down: increase y
    'a': (-1, 0),   # Left: decrease x
    'd': (1, 0),    # Right: increase x
}

# Opposite direction mapping
OPPOSITE_DIRECTIONS: Dict[str, str] = {
    'w': 's',
    's': 'w',
    'a': 'd',
    'd': 'a'
}


def get_direction_offset(direction: str) -> Tuple[int, int]:
    """
    Get the coordinate offset for a given direction.
    
    Args:
        direction: Direction string ('w', 's', 'a', 'd')
        
    Returns:
        Tuple of (dx, dy) offset values
        
    Raises:
        ValueError: If direction is invalid
    """
    if direction not in DIRECTION_OFFSETS:
        raise ValueError(f"Invalid direction: {direction}. Must be one of {list(DIRECTION_OFFSETS.keys())}")
    
    return DIRECTION_OFFSETS[direction]


def get_opposite_direction(direction: str) -> str:
    """
    Get the opposite direction for a given direction.
    
    Args:
        direction: Direction string ('w', 's', 'a', 'd')
        
    Returns:
        Opposite direction string
        
    Raises:
        ValueError: If direction is invalid
    """
    if direction not in OPPOSITE_DIRECTIONS:
        raise ValueError(f"Invalid direction: {direction}. Must be one of {list(OPPOSITE_DIRECTIONS.keys())}")
    
    return OPPOSITE_DIRECTIONS[direction]


def is_valid_coordinate(x: int, y: int, width: int, height: int) -> bool:
    """
    Check if coordinates are valid within given bounds.
    
    Args:
        x: X coordinate
        y: Y coordinate
        width: Maximum width (exclusive)
        height: Maximum height (exclusive)
        
    Returns:
        True if coordinates are valid, False otherwise
    """
    return 0 <= x < width and 0 <= y < height


def get_new_position(x: int, y: int, direction: str) -> Tuple[int, int]:
    """
    Calculate new position after moving in a direction.
    
    Args:
        x: Current x coordinate
        y: Current y coordinate
        direction: Direction to move ('w', 's', 'a', 'd')
        
    Returns:
        New (x, y) position
        
    Raises:
        ValueError: If direction is invalid
    """
    dx, dy = get_direction_offset(direction)
    return (x + dx, y + dy)


def calculate_distance(x1: int, y1: int, x2: int, y2: int) -> float:
    """
    Calculate Euclidean distance between two points.
    
    Args:
        x1, y1: First point coordinates
        x2, y2: Second point coordinates
        
    Returns:
        Euclidean distance
    """
    return np.sqrt((x2 - x1)**2 + (y2 - y1)**2)


def calculate_manhattan_distance(x1: int, y1: int, x2: int, y2: int) -> int:
    """
    Calculate Manhattan distance between two points.
    
    Args:
        x1, y1: First point coordinates
        x2, y2: Second point coordinates
        
    Returns:
        Manhattan distance
    """
    return abs(x2 - x1) + abs(y2 - y1)


def normalize_coordinates(x: int, y: int, width: int, height: int) -> Tuple[float, float]:
    """
    Normalize coordinates to [0, 1] range.
    
    Args:
        x, y: Coordinates to normalize
        width, height: Dimensions for normalization
        
    Returns:
        Normalized (x, y) coordinates
    """
    return (x / (width - 1), y / (height - 1))