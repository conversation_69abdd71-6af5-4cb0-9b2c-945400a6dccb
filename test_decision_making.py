#!/usr/bin/env python3
"""
Test script for agent decision-making algorithms (Task 5.3).

This script tests the pheromone-based decision algorithms implemented in task 5.3:
- Basic pheromone-based decision making (requirement 6.1)
- Random selection for tied directions (requirement 6.2)
- Decision making with noise to avoid local optima
- Gradient-based decision making alternative
"""

import numpy as np
import sys
import os

# Add the project directory to the path
sys.path.insert(0, os.path.abspath('.'))

from pheromone_maze.maze import Maze
from pheromone_maze.pheromone_field import Pheromone<PERSON>ield
from pheromone_maze.agent import Agent
from pheromone_maze.config import SimulationConfig


def test_basic_decision_making():
    """Test basic pheromone-based decision making."""
    print("=== Testing Basic Pheromone-Based Decision Making ===")
    
    # Create a simple 5x5 maze with a fixed seed for reproducibility
    config = SimulationConfig(maze_width=5, maze_height=5, obstacle_ratio=0.2)
    maze = Maze(config.maze_width, config.maze_height, config.obstacle_ratio, seed=42)
    
    # Find valid start and target positions
    start_pos = None
    target_pos = None
    
    # Look for valid positions
    for x in range(1, 4):
        for y in range(1, 4):
            if not maze.is_obstacle(x, y):
                if start_pos is None:
                    start_pos = (x, y)
                elif target_pos is None and (x, y) != start_pos:
                    target_pos = (x, y)
                    break
        if start_pos and target_pos:
            break
    
    # Set positions
    if start_pos:
        maze.set_start_position(start_pos[0], start_pos[1])
    else:
        maze.set_start_position(0, 0)  # Fallback to corner
    
    if target_pos:
        maze.set_target_position(target_pos[0], target_pos[1])
    else:
        maze.set_target_position(4, 4)  # Fallback to opposite corner
    
    # Create pheromone field and agent
    pheromone_field = PheromoneField(config.maze_width, config.maze_height, config)
    start_x, start_y = maze.get_start_position() or start_pos or (0, 0)
    agent = Agent(start_x, start_y, maze, pheromone_field, config)
    
    # Set up a clear pheromone gradient - target should be more attractive
    if target_pos:
        pheromone_field.add_target_pheromone(target_pos[0], target_pos[1])
    pheromone_field.update_target_pheromones()
    
    print(f"Agent starts at: {agent.get_position()}")
    print(f"Target is at: {maze.get_target_position()}")
    
    # Test decision making
    print("\n--- Testing decision algorithms ---")
    
    # Test pheromone-based decision
    decision1 = agent.decide_next_move_pheromone_based()
    print(f"Pheromone-based decision: {decision1}")
    
    # Test decision with noise
    decision2 = agent.decide_next_move_with_noise(0.1)
    print(f"Decision with noise: {decision2}")
    
    # Test gradient-based decision
    decision3 = agent.decide_next_move_gradient_based()
    print(f"Gradient-based decision: {decision3}")
    
    # Show perception data
    print("\n--- Perception Data ---")
    agent.print_pheromone_perception()
    
    print("\n--- Decision Analysis ---")
    agent.print_decision_analysis()
    
    print("✓ Basic decision making test completed\n")


def test_tie_breaking_randomness():
    """Test random selection when multiple directions have equal strength."""
    print("=== Testing Tie-Breaking Randomness (Requirement 6.2) ===")
    
    # Create maze with agent in center to test ties - minimal obstacles
    config = SimulationConfig(maze_width=5, maze_height=5, obstacle_ratio=0.1)  # Minimal obstacles
    maze = Maze(config.maze_width, config.maze_height, 0.1, seed=42)  # Minimal obstacles
    maze.set_start_position(2, 2)  # Center position
    maze.set_target_position(4, 4)
    
    pheromone_field = PheromoneField(config.maze_width, config.maze_height, config)
    agent = Agent(2, 2, maze, pheromone_field, config)
    
    # Initially, all directions should have similar pheromone strength
    print(f"Agent at center position: {agent.get_position()}")
    
    # Test multiple decisions to see randomness
    decisions = []
    for i in range(10):
        decision = agent.decide_next_move_pheromone_based()
        decisions.append(decision)
    
    print(f"10 decisions made: {decisions}")
    print(f"Unique decisions: {set(decisions)}")
    
    # Should have some variety in decisions (not all the same)
    if len(set(decisions)) > 1:
        print("✓ Randomness in tie-breaking detected")
    else:
        print("⚠ All decisions were the same - might indicate deterministic behavior")
    
    print("✓ Tie-breaking randomness test completed\n")


def test_decision_with_obstacles():
    """Test decision making with obstacles creating pheromone fields."""
    print("=== Testing Decision Making with Obstacles ===")
    
    config = SimulationConfig(maze_width=7, maze_height=7, obstacle_ratio=0.3)
    maze = Maze(config.maze_width, config.maze_height, config.obstacle_ratio, seed=42)
    
    # Find a valid start position
    start_pos = None
    for x in range(1, 6):
        for y in range(1, 6):
            if not maze.is_obstacle(x, y):
                start_pos = (x, y)
                break
        if start_pos:
            break
    
    if not start_pos:
        start_pos = (0, 0)  # Fallback - corners are usually clear
    
    maze.set_start_position(start_pos[0], start_pos[1])
    
    # Find a valid target position
    target_pos = None
    for x in range(5, 6):
        for y in range(5, 6):
            if not maze.is_obstacle(x, y) and (x, y) != start_pos:
                target_pos = (x, y)
                break
    
    if not target_pos:
        target_pos = (6, 6)  # Fallback
    
    maze.set_target_position(target_pos[0], target_pos[1])
        
    pheromone_field = PheromoneField(config.maze_width, config.maze_height, config)
    agent = Agent(start_pos[0], start_pos[1], maze, pheromone_field, config)
    
    # Add some obstacle pheromone
    agent.move('w')  # This should fail and add obstacle pheromone
    agent.move('a')  # This should also fail
    
    # Add target pheromone
    pheromone_field.update_target_pheromones()
    
    print(f"Agent position: {agent.get_position()}")
    print(f"Collisions so far: {agent.get_collision_count()}")
    
    # Show current pheromone field perception
    print("\n--- Pheromone Perception After Collisions ---")
    agent.print_pheromone_perception()
    
    # Test decision making
    print("\n--- Decision Making Analysis ---")
    agent.print_decision_analysis()
    
    # Test the actual execution methods
    print("\n--- Testing Execution Methods ---")
    
    for i in range(5):
        print(f"\nStep {i+1}:")
        success = agent.execute_pheromone_based_move()
        print(f"  Move successful: {success}")
        print(f"  New position: {agent.get_position()}")
        print(f"  At target: {agent.is_at_target()}")
        
        if agent.is_at_target():
            print("🎯 Target reached!")
            break
    
    print("✓ Decision making with obstacles test completed\n")


def test_navigation_simulation():
    """Test a complete navigation simulation using pheromone-based decisions."""
    print("=== Testing Complete Navigation Simulation ===")
    
    config = SimulationConfig(
        maze_width=10, 
        maze_height=10, 
        obstacle_ratio=0.25,
        max_steps=100,
        decision_noise_std=0.05
    )
    
    maze = Maze(config.maze_width, config.maze_height, config.obstacle_ratio, seed=42)
    
    # Find valid start and target positions
    start_pos = None
    target_pos = None
    
    for x in range(1, 3):
        for y in range(1, 3):
            if not maze.is_obstacle(x, y):
                start_pos = (x, y)
                break
        if start_pos:
            break
    
    for x in range(7, 9):
        for y in range(7, 9):
            if not maze.is_obstacle(x, y):
                target_pos = (x, y)
                break
        if target_pos:
            break
    
    if not start_pos:
        start_pos = (0, 0)
    if not target_pos:
        target_pos = (9, 9)
    
    maze.set_start_position(start_pos[0], start_pos[1])
    maze.set_target_position(target_pos[0], target_pos[1])
    
    pheromone_field = PheromoneField(config.maze_width, config.maze_height, config)
    agent = Agent(start_pos[0], start_pos[1], maze, pheromone_field, config)
    
    # Initialize target pheromone field
    pheromone_field.update_target_pheromones()
    
    print(f"Starting navigation simulation:")
    print(f"  Start: {agent.get_position()}")
    print(f"  Target: {maze.get_target_position()}")
    print(f"  Max steps: {config.max_steps}")
    
    # Run navigation simulation
    for step in range(config.max_steps):
        # Execute one step of navigation
        success = agent.execute_pheromone_based_move()
        
        # Update pheromone field (decay)
        pheromone_field.update_field()
        
        # Check if target reached
        if agent.is_at_target():
            print(f"\n🎯 Navigation successful!")
            print(f"  Steps taken: {step + 1}")
            print(f"  Final position: {agent.get_position()}")
            break
        
        # Print progress every 10 steps
        if (step + 1) % 10 == 0:
            print(f"  Step {step + 1}: Position {agent.get_position()}")
    
    else:
        print(f"\n⚠ Navigation incomplete after {config.max_steps} steps")
        print(f"  Final position: {agent.get_position()}")
        print(f"  Distance to target: {agent.get_agent_statistics()['distance_to_target']:.2f}")
    
    # Show final statistics
    stats = agent.get_agent_statistics()
    print(f"\nFinal Statistics:")
    print(f"  Total moves attempted: {stats['total_moves_attempted']}")
    print(f"  Successful moves: {stats['successful_moves']}")
    print(f"  Collisions: {stats['collision_count']}")
    print(f"  Success rate: {stats['success_rate']:.2%}")
    print(f"  Path length: {stats['path_length']}")
    
    print("✓ Complete navigation simulation test completed\n")


def main():
    """Run all decision-making tests."""
    print("Testing Agent Decision-Making Algorithms (Task 5.3)")
    print("=" * 60)
    
    try:
        test_basic_decision_making()
        test_tie_breaking_randomness()
        test_decision_with_obstacles()
        test_navigation_simulation()
        
        print("🎉 All decision-making tests completed successfully!")
        print("\nTask 5.3 Implementation Summary:")
        print("✓ Basic pheromone-based decision making (Requirement 6.1)")
        print("✓ Random selection for tied directions (Requirement 6.2)")
        print("✓ Decision making with noise to avoid local optima")
        print("✓ Alternative gradient-based decision algorithm")
        print("✓ Complete navigation execution methods")
        print("✓ Decision analysis and debugging tools")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
