"""
Agent module for the pheromone-based navigation system.

This module implements the Agent class that can move through the maze,
perceive pheromone fields, and make navigation decisions based on
pheromone gradients.
"""

import random
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from .config import SimulationConfig, AgentState
from .maze import Maze
from .pheromone_field import <PERSON><PERSON>mone<PERSON><PERSON>
from .utils import Direction, get_new_position, is_valid_coordinate
from .exceptions import InvalidPositionException


class Agent:
    """
    Intelligent agent that navigates through the maze using pheromone-based decisions.
    
    The agent can:
    - Move in four directions (WASD)
    - Perceive pheromone fields in all directions
    - Make movement decisions based on pheromone gradients
    - Track movement history and detect target arrival
    """
    
    def __init__(self, x: int, y: int, maze: Maze, pheromone_field: PheromoneField, 
                 config: Optional[SimulationConfig] = None):
        """
        Initialize the agent at the specified position.
        
        Args:
            x: Initial x coordinate
            y: Initial y coordinate
            maze: Maze environment reference
            pheromone_field: Pheromone field reference
            config: Configuration object
            
        Raises:
            InvalidPositionException: If initial position is invalid
        """
        if not maze.is_valid_position(x, y):
            raise InvalidPositionException(x, y, "Agent initial position is out of bounds")
        
        if maze.is_obstacle(x, y):
            raise InvalidPositionException(x, y, "Agent cannot be placed on an obstacle")
        
        self.maze = maze
        self.pheromone_field = pheromone_field
        self.config = config or SimulationConfig()
        
        # Initialize agent state
        self.state = AgentState(
            x=x,
            y=y,
            move_count=0,
            path_history=[(x, y)],
            last_pheromone_perception={}
        )
        
        # Movement tracking
        self.collision_count = 0
        self.successful_moves = 0
        self.last_collision_position: Optional[Tuple[int, int]] = None
        
        # Decision making state
        self.last_decision_direction: Optional[str] = None
        self.decision_history: List[str] = []
        
        # Target detection
        self._target_reached = False
        self._steps_at_target = 0
    
    def get_position(self) -> Tuple[int, int]:
        """
        Get the current position of the agent.
        
        Returns:
            Tuple of (x, y) coordinates
        """
        return (self.state.x, self.state.y)
    
    def get_move_history(self) -> List[Tuple[int, int]]:
        """
        Get the complete movement history of the agent.
        
        Returns:
            List of (x, y) position tuples in chronological order
        """
        return self.state.path_history.copy()
    
    def get_move_count(self) -> int:
        """
        Get the total number of moves attempted by the agent.
        
        Returns:
            Number of moves (including failed collision attempts)
        """
        return self.state.move_count
    
    def get_successful_move_count(self) -> int:
        """
        Get the number of successful moves by the agent.
        
        Returns:
            Number of successful moves (excluding collisions)
        """
        return self.successful_moves
    
    def get_collision_count(self) -> int:
        """
        Get the number of collisions with obstacles.
        
        Returns:
            Number of collision attempts
        """
        return self.collision_count
    
    def is_at_target(self) -> bool:
        """
        Check if the agent has reached the target position.
        
        Returns:
            True if agent is at target position, False otherwise
        """
        target_pos = self.maze.get_target_position()
        if target_pos is None:
            return False
        
        current_pos = self.get_position()
        return current_pos == target_pos
    
    def move(self, direction: str) -> bool:
        """
        Attempt to move the agent in the specified direction.
        
        Args:
            direction: Direction to move ('w', 's', 'a', 'd')
            
        Returns:
            True if move was successful, False if blocked by obstacle or boundary
            
        Raises:
            ValueError: If direction is invalid
        """
        if direction not in Direction.all_directions():
            raise ValueError(f"Invalid direction: {direction}. Must be one of {Direction.all_directions()}")
        
        # Calculate new position
        current_x, current_y = self.get_position()
        new_x, new_y = get_new_position(current_x, current_y, direction)
        
        # Increment move count (includes failed attempts)
        self.state.move_count += 1
        
        # Record decision
        self.last_decision_direction = direction
        self.decision_history.append(direction)
        
        # Check if new position is valid
        if not self.maze.is_valid_position(new_x, new_y):
            # Hit boundary - treat as collision
            self._handle_collision(current_x, current_y, direction, "boundary")
            return False
        
        # Check if new position is an obstacle
        if self.maze.is_obstacle(new_x, new_y):
            # Hit obstacle - treat as collision
            self._handle_collision(current_x, current_y, direction, "obstacle")
            return False
        
        # Move is valid - update position
        self.state.x = new_x
        self.state.y = new_y
        self.state.add_to_path(new_x, new_y)
        self.successful_moves += 1
        
        # Check if reached target
        if self.is_at_target():
            self._target_reached = True
            self._steps_at_target += 1
        else:
            self._steps_at_target = 0
        
        return True
    
    def move_up(self) -> bool:
        """Move agent up (decrease y coordinate)."""
        return self.move('w')
    
    def move_down(self) -> bool:
        """Move agent down (increase y coordinate)."""
        return self.move('s')
    
    def move_left(self) -> bool:
        """Move agent left (decrease x coordinate)."""
        return self.move('a')
    
    def move_right(self) -> bool:
        """Move agent right (increase x coordinate)."""
        return self.move('d')
    
    def _handle_collision(self, x: int, y: int, direction: str, collision_type: str) -> None:
        """
        Handle collision with obstacle or boundary.
        
        Args:
            x: X coordinate where collision occurred
            y: Y coordinate where collision occurred
            direction: Direction of attempted move
            collision_type: Type of collision ("obstacle" or "boundary")
        """
        self.collision_count += 1
        self.last_collision_position = (x, y)
        
        # Add obstacle pheromone at collision position
        # This is part of the pheromone-based navigation mechanism
        self.pheromone_field.add_obstacle_pheromone(x, y)
    
    def has_reached_target(self) -> bool:
        """
        Check if agent has successfully reached and stayed at target.
        
        Returns:
            True if agent is currently at target position
        """
        return self.is_at_target()
    
    def get_steps_at_target(self) -> int:
        """
        Get number of consecutive steps agent has been at target.
        
        Returns:
            Number of steps at target position
        """
        return self._steps_at_target
    
    def reset_position(self, x: int, y: int) -> None:
        """
        Reset agent to a new position (for testing or restarting).
        
        Args:
            x: New x coordinate
            y: New y coordinate
            
        Raises:
            InvalidPositionException: If position is invalid
        """
        if not self.maze.is_valid_position(x, y):
            raise InvalidPositionException(x, y, "Reset position is out of bounds")
        
        if self.maze.is_obstacle(x, y):
            raise InvalidPositionException(x, y, "Reset position cannot be on an obstacle")
        
        # Reset state
        self.state.x = x
        self.state.y = y
        self.state.move_count = 0
        self.state.path_history = [(x, y)]
        self.state.last_pheromone_perception = {}
        
        # Reset tracking variables
        self.collision_count = 0
        self.successful_moves = 0
        self.last_collision_position = None
        self.last_decision_direction = None
        self.decision_history = []
        self._target_reached = False
        self._steps_at_target = 0
        
        # Check if reset position is at target
        if self.is_at_target():
            self._target_reached = True
    
    def get_agent_statistics(self) -> Dict[str, Any]:
        """
        Get comprehensive statistics about the agent's performance.
        
        Returns:
            Dictionary containing agent statistics
        """
        current_pos = self.get_position()
        target_pos = self.maze.get_target_position()
        
        # Calculate distance to target if target exists
        distance_to_target = None
        if target_pos is not None:
            dx = current_pos[0] - target_pos[0]
            dy = current_pos[1] - target_pos[1]
            distance_to_target = np.sqrt(dx*dx + dy*dy)
        
        # Calculate success rate
        total_attempts = self.state.move_count
        success_rate = self.successful_moves / total_attempts if total_attempts > 0 else 0.0
        
        return {
            'current_position': current_pos,
            'target_position': target_pos,
            'distance_to_target': distance_to_target,
            'total_moves_attempted': total_attempts,
            'successful_moves': self.successful_moves,
            'collision_count': self.collision_count,
            'success_rate': success_rate,
            'at_target': self.is_at_target(),
            'target_reached': self._target_reached,
            'steps_at_target': self._steps_at_target,
            'path_length': len(self.state.path_history),
            'last_collision_position': self.last_collision_position,
            'last_decision': self.last_decision_direction,
            'decision_history_length': len(self.decision_history)
        }
    
    # ========================================
    # Pheromone Perception Methods (Task 5.2)
    # ========================================
    
    def perceive_pheromone_strength(self, direction: str) -> float:
        """
        Perceive pheromone strength in a specific direction.
        
        Args:
            direction: Direction to sense ('w', 's', 'a', 'd')
            
        Returns:
            Combined pheromone strength in the specified direction
            
        Raises:
            ValueError: If direction is invalid
        """
        if direction not in Direction.all_directions():
            raise ValueError(f"Invalid direction: {direction}. Must be one of {Direction.all_directions()}")
        
        current_x, current_y = self.get_position()
        strength = self.pheromone_field.get_directional_pheromone_strength(
            current_x, current_y, direction, self.maze
        )
        
        # Update cached perception data
        self.state.last_pheromone_perception[direction] = strength
        
        return strength
    
    def perceive_all_directions(self) -> Dict[str, float]:
        """
        Perceive pheromone strengths in all four directions.
        
        Returns:
            Dictionary mapping directions ('w', 's', 'a', 'd') to pheromone strengths
        """
        current_x, current_y = self.get_position()
        all_strengths = self.pheromone_field.get_all_directional_strengths(
            current_x, current_y, self.maze
        )
        
        # Update cached perception data
        self.state.last_pheromone_perception.update(all_strengths)
        
        return all_strengths
    
    def perceive_pheromone_gradient(self) -> Dict[str, float]:
        """
        Perceive pheromone gradients in all directions.
        
        Gradients represent the difference between current position and
        neighboring positions, useful for detecting pheromone flow direction.
        
        Returns:
            Dictionary mapping directions to gradient values
        """
        current_x, current_y = self.get_position()
        gradients = self.pheromone_field.calculate_pheromone_gradient(
            current_x, current_y, self.maze
        )
        
        # Store gradient information in perception cache with special prefix
        gradient_cache = {f"gradient_{direction}": value for direction, value in gradients.items()}
        self.state.last_pheromone_perception.update(gradient_cache)
        
        return gradients
    
    def get_cached_pheromone_perception(self) -> Dict[str, float]:
        """
        Get the last cached pheromone perception data.
        
        Returns:
            Dictionary containing the most recent pheromone perceptions
        """
        return self.state.last_pheromone_perception.copy()
    
    def get_pheromone_field_data(self) -> Dict[str, Any]:
        """
        Get comprehensive pheromone field data at current position.
        
        Returns:
            Dictionary containing detailed pheromone field information
        """
        current_x, current_y = self.get_position()
        
        # Get individual field values
        obstacle_strength = self.pheromone_field.get_obstacle_pheromone(current_x, current_y)
        target_strength = self.pheromone_field.get_target_pheromone(current_x, current_y)
        combined_strength = self.pheromone_field.get_combined_pheromone(current_x, current_y)
        
        # Get directional information
        directional_strengths = self.perceive_all_directions()
        gradients = self.perceive_pheromone_gradient()
        
        return {
            'position': (current_x, current_y),
            'current_obstacle_pheromone': obstacle_strength,
            'current_target_pheromone': target_strength,
            'current_combined_pheromone': combined_strength,
            'directional_strengths': directional_strengths,
            'gradients': gradients,
            'timestamp': self.state.move_count
        }
    
    def clear_pheromone_perception_cache(self) -> None:
        """Clear the pheromone perception cache."""
        self.state.last_pheromone_perception.clear()
    
    def format_pheromone_perception(self, include_gradients: bool = True) -> str:
        """
        Format pheromone perception data for debugging output.
        
        Args:
            include_gradients: Whether to include gradient information
            
        Returns:
            Formatted string representation of pheromone perception
        """
        current_x, current_y = self.get_position()
        data = self.get_pheromone_field_data()
        
        lines = [
            f"=== Pheromone Perception at ({current_x}, {current_y}) ===",
            f"Move: {self.state.move_count}",
            f"Current Field Values:",
            f"  Obstacle: {data['current_obstacle_pheromone']:.3f}",
            f"  Target:   {data['current_target_pheromone']:.3f}",
            f"  Combined: {data['current_combined_pheromone']:.3f}",
            "",
            f"Directional Strengths:",
        ]
        
        # Format directional strengths
        directions_order = ['w', 'd', 's', 'a']  # Up, Right, Down, Left
        direction_names = {'w': 'Up   ', 'd': 'Right', 's': 'Down ', 'a': 'Left '}
        
        for direction in directions_order:
            strength = data['directional_strengths'].get(direction, 0.0)
            strength_str = f"{strength:.3f}" if strength != float('-inf') else "WALL"
            lines.append(f"  {direction_names[direction]}: {strength_str}")
        
        if include_gradients:
            lines.extend([
                "",
                f"Gradients (neighbor - current):",
            ])
            
            for direction in directions_order:
                gradient = data['gradients'].get(direction, 0.0)
                gradient_str = f"{gradient:.3f}" if gradient != float('-inf') else "WALL"
                lines.append(f"  {direction_names[direction]}: {gradient_str}")
        
        lines.append("=" * 50)
        return "\n".join(lines)
    
    def print_pheromone_perception(self, include_gradients: bool = True) -> None:
        """
        Print formatted pheromone perception data to console.
        
        Args:
            include_gradients: Whether to include gradient information
        """
        print(self.format_pheromone_perception(include_gradients))
    
    def get_strongest_pheromone_direction(self) -> Tuple[str, float]:
        """
        Get the direction with the strongest (most positive) pheromone.
        
        Returns:
            Tuple of (direction, strength) for the strongest direction
        """
        strengths = self.perceive_all_directions()
        
        # Filter out wall directions (negative infinity)
        valid_directions = {d: s for d, s in strengths.items() if s != float('-inf')}
        
        if not valid_directions:
            # All directions are walls - this shouldn't happen in normal conditions
            return ('w', float('-inf'))
        
        best_direction = max(valid_directions.keys(), key=lambda d: valid_directions[d])
        best_strength = valid_directions[best_direction]
        
        return (best_direction, best_strength)
    
    def get_steepest_gradient_direction(self) -> Tuple[str, float]:
        """
        Get the direction with the steepest positive gradient.
        
        Returns:
            Tuple of (direction, gradient) for the steepest positive gradient
        """
        gradients = self.perceive_pheromone_gradient()
        
        # Filter out wall directions (negative infinity)
        valid_gradients = {d: g for d, g in gradients.items() if g != float('-inf')}
        
        if not valid_gradients:
            # All directions are walls
            return ('w', float('-inf'))
        
        best_direction = max(valid_gradients.keys(), key=lambda d: valid_gradients[d])
        best_gradient = valid_gradients[best_direction]
        
        return (best_direction, best_gradient)

    # ========================================
    # Decision Making Methods (Task 5.3)
    # ========================================
    
    def decide_next_move_pheromone_based(self) -> str:
        """
        Make a movement decision based on pheromone field strength.
        
        Implementation of requirement 6.1: Choose direction with highest pheromone strength.
        Implementation of requirement 6.2: Random selection when multiple directions are tied.
        
        Returns:
            Direction to move ('w', 's', 'a', 'd')
            
        Raises:
            RuntimeError: If no valid move directions are available (shouldn't happen in normal maze)
        """
        # Get pheromone strengths in all directions
        strengths = self.perceive_all_directions()
        
        # Filter out wall directions (negative infinity)
        valid_directions = {d: s for d, s in strengths.items() if s != float('-inf')}
        
        if not valid_directions:
            # This shouldn't happen in a proper maze - agent would be completely surrounded
            raise RuntimeError("Agent is completely surrounded by walls - no valid moves available")
        
        # Find the maximum pheromone strength
        max_strength = max(valid_directions.values())
        
        # Get all directions with maximum strength (handles ties)
        best_directions = [d for d, s in valid_directions.items() if s == max_strength]
        
        # Random selection among tied directions (requirement 6.2)
        chosen_direction = random.choice(best_directions)
        
        return chosen_direction
    
    def decide_next_move_with_noise(self, noise_factor: Optional[float] = None) -> str:
        """
        Make a movement decision with optional random noise to avoid local optima.
        
        Adds small random noise to pheromone values before making decision,
        which can help escape local minima in the pheromone field.
        
        Args:
            noise_factor: Standard deviation of noise to add. If None, uses config value.
            
        Returns:
            Direction to move ('w', 's', 'a', 'd')
        """
        if noise_factor is None:
            noise_factor = self.config.decision_noise_std
        
        # Get pheromone strengths in all directions
        strengths = self.perceive_all_directions()
        
        # Filter out wall directions (negative infinity)
        valid_directions = {d: s for d, s in strengths.items() if s != float('-inf')}
        
        if not valid_directions:
            raise RuntimeError("Agent is completely surrounded by walls - no valid moves available")
        
        # Add random noise to each direction's strength
        noisy_strengths = {}
        for direction, strength in valid_directions.items():
            noise = np.random.normal(0, noise_factor)
            noisy_strengths[direction] = strength + noise
        
        # Find direction with maximum noisy strength
        best_direction = max(noisy_strengths.keys(), key=lambda d: noisy_strengths[d])
        
        return best_direction
    
    def decide_next_move_gradient_based(self) -> str:
        """
        Make a movement decision based on pheromone gradient (steepest ascent).
        
        Alternative decision strategy that follows the steepest positive gradient
        rather than absolute pheromone strength.
        
        Returns:
            Direction to move ('w', 's', 'a', 'd')
        """
        # Get pheromone gradients in all directions
        gradients = self.perceive_pheromone_gradient()
        
        # Filter out wall directions (negative infinity)
        valid_gradients = {d: g for d, g in gradients.items() if g != float('-inf')}
        
        if not valid_gradients:
            raise RuntimeError("Agent is completely surrounded by walls - no valid moves available")
        
        # Find maximum gradient
        max_gradient = max(valid_gradients.values())
        
        # Get all directions with maximum gradient (handles ties)
        best_directions = [d for d, g in valid_gradients.items() if g == max_gradient]
        
        # Random selection among tied directions
        chosen_direction = random.choice(best_directions)
        
        return chosen_direction
    
    def execute_pheromone_based_move(self) -> bool:
        """
        Execute one step of pheromone-based navigation.
        
        This is the main navigation method that combines decision making with movement execution.
        Uses the primary pheromone-based decision algorithm.
        
        Returns:
            True if move was successful, False if blocked or failed
        """
        try:
            # Make decision based on pheromone field
            chosen_direction = self.decide_next_move_pheromone_based()
            
            # Execute the move
            success = self.move(chosen_direction)
            
            return success
            
        except RuntimeError as e:
            # Log the error but don't crash - this might happen in edge cases
            print(f"Navigation error: {e}")
            return False
    
    def execute_noisy_pheromone_move(self, noise_factor: Optional[float] = None) -> bool:
        """
        Execute one step of noisy pheromone-based navigation.
        
        Uses pheromone-based decision making with added noise to help escape local optima.
        
        Args:
            noise_factor: Standard deviation of noise to add
            
        Returns:
            True if move was successful, False if blocked or failed
        """
        try:
            # Make decision with noise
            chosen_direction = self.decide_next_move_with_noise(noise_factor)
            
            # Execute the move
            success = self.move(chosen_direction)
            
            return success
            
        except RuntimeError as e:
            print(f"Navigation error: {e}")
            return False
    
    def execute_gradient_based_move(self) -> bool:
        """
        Execute one step of gradient-based navigation.
        
        Uses gradient-based decision making (steepest ascent).
        
        Returns:
            True if move was successful, False if blocked or failed
        """
        try:
            # Make decision based on gradients
            chosen_direction = self.decide_next_move_gradient_based()
            
            # Execute the move
            success = self.move(chosen_direction)
            
            return success
            
        except RuntimeError as e:
            print(f"Navigation error: {e}")
            return False
    
    def get_decision_analysis(self) -> Dict[str, Any]:
        """
        Get detailed analysis of current decision-making state.
        
        Useful for debugging and understanding agent behavior.
        
        Returns:
            Dictionary containing decision analysis data
        """
        # Get current perception data
        strengths = self.perceive_all_directions()
        gradients = self.perceive_pheromone_gradient()
        
        # Filter valid directions
        valid_directions = {d: s for d, s in strengths.items() if s != float('-inf')}
        valid_gradients = {d: g for d, g in gradients.items() if g != float('-inf')}
        
        # Determine what each decision method would choose
        analysis = {
            'current_position': self.get_position(),
            'valid_directions': list(valid_directions.keys()),
            'directional_strengths': strengths,
            'directional_gradients': gradients,
        }
        
        if valid_directions:
            # Pheromone-based decision
            max_strength = max(valid_directions.values())
            pheromone_choices = [d for d, s in valid_directions.items() if s == max_strength]
            analysis['pheromone_based'] = {
                'max_strength': max_strength,
                'tied_directions': pheromone_choices,
                'would_choose': random.choice(pheromone_choices) if pheromone_choices else None
            }
            
            # Gradient-based decision
            if valid_gradients:
                max_gradient = max(valid_gradients.values())
                gradient_choices = [d for d, g in valid_gradients.items() if g == max_gradient]
                analysis['gradient_based'] = {
                    'max_gradient': max_gradient,
                    'tied_directions': gradient_choices,
                    'would_choose': random.choice(gradient_choices) if gradient_choices else None
                }
        
        return analysis
    
    def format_decision_analysis(self) -> str:
        """
        Format decision analysis data for debugging output.
        
        Returns:
            Formatted string representation of decision analysis
        """
        analysis = self.get_decision_analysis()
        current_x, current_y = analysis['current_position']
        
        lines = [
            f"=== Decision Analysis at ({current_x}, {current_y}) ===",
            f"Move: {self.state.move_count}",
            f"Valid directions: {analysis['valid_directions']}",
            "",
            "Directional Analysis:"
        ]
        
        # Format directional information
        directions_order = ['w', 'd', 's', 'a']
        direction_names = {'w': 'Up   ', 'd': 'Right', 's': 'Down ', 'a': 'Left '}
        
        for direction in directions_order:
            if direction in analysis['valid_directions']:
                strength = analysis['directional_strengths'][direction]
                gradient = analysis['directional_gradients'][direction]
                lines.append(f"  {direction_names[direction]}: Strength={strength:.3f}, Gradient={gradient:.3f}")
            else:
                lines.append(f"  {direction_names[direction]}: WALL")
        
        # Add decision recommendations
        if 'pheromone_based' in analysis:
            pheromone_data = analysis['pheromone_based']
            lines.extend([
                "",
                f"Pheromone-based decision:",
                f"  Max strength: {pheromone_data['max_strength']:.3f}",
                f"  Tied directions: {pheromone_data['tied_directions']}",
                f"  Would choose: {pheromone_data['would_choose']}"
            ])
        
        if 'gradient_based' in analysis:
            gradient_data = analysis['gradient_based']
            lines.extend([
                "",
                f"Gradient-based decision:",
                f"  Max gradient: {gradient_data['max_gradient']:.3f}",
                f"  Tied directions: {gradient_data['tied_directions']}",
                f"  Would choose: {gradient_data['would_choose']}"
            ])
        
        lines.append("=" * 50)
        return "\n".join(lines)
    
    def print_decision_analysis(self) -> None:
        """Print formatted decision analysis to console."""
        print(self.format_decision_analysis())

    def __str__(self) -> str:
        """String representation of the agent for debugging."""
        stats = self.get_agent_statistics()
        return (
            f"Agent at {stats['current_position']}, "
            f"Moves: {stats['successful_moves']}/{stats['total_moves_attempted']}, "
            f"Collisions: {stats['collision_count']}, "
            f"At target: {stats['at_target']}"
        )