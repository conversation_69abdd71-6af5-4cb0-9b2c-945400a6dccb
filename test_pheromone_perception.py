"""
Test script for Agent pheromone perception functionality (Task 5.2).

This script tests the newly implemented pheromone perception methods
to ensure they work correctly with the existing pheromone field system.
"""

import sys
import os

# Add the pheromone_maze package to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '.'))

from pheromone_maze.maze import Maze
from pheromone_maze.pheromone_field import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from pheromone_maze.agent import Agent
from pheromone_maze.config import SimulationConfig


def test_pheromone_perception():
    """Test the pheromone perception functionality."""
    
    print("=== Testing Agent Pheromone Perception (Task 5.2) ===\n")
    
    # Create a small test environment
    config = SimulationConfig(
        maze_width=5,
        maze_height=5,
        obstacle_ratio=0.2,
        obstacle_pheromone_intensity=-5.0,
        target_pheromone_intensity=10.0
    )
    
    # Create maze and pheromone field
    maze = Maze(config.maze_width, config.maze_height, config.obstacle_ratio)
    maze.set_start_position(1, 1)
    maze.set_target_position(3, 3)
    
    pheromone_field = PheromoneField(config.maze_width, config.maze_height, config)
    pheromone_field.set_target_position(3, 3)
    pheromone_field.generate_target_pheromone_field()
    
    # Create agent
    agent = Agent(1, 1, maze, pheromone_field, config)
    
    print("Test Environment:")
    print(f"  Maze size: {config.maze_width}x{config.maze_height}")
    print(f"  Agent position: {agent.get_position()}")
    print(f"  Target position: {maze.get_target_position()}")
    print()
    
    # Test 1: Basic direction perception
    print("Test 1: Basic Direction Perception")
    print("-" * 40)
    
    try:
        # Test individual direction perception
        up_strength = agent.perceive_pheromone_strength('w')
        right_strength = agent.perceive_pheromone_strength('d')
        down_strength = agent.perceive_pheromone_strength('s')
        left_strength = agent.perceive_pheromone_strength('a')
        
        print(f"Individual direction strengths:")
        print(f"  Up (w):    {up_strength:.3f}")
        print(f"  Right (d): {right_strength:.3f}")
        print(f"  Down (s):  {down_strength:.3f}")
        print(f"  Left (a):  {left_strength:.3f}")
        print("✓ Individual direction perception works")
        
    except Exception as e:
        print(f"✗ Individual direction perception failed: {e}")
        return False
    
    print()
    
    # Test 2: All directions perception
    print("Test 2: All Directions Perception")
    print("-" * 40)
    
    try:
        all_strengths = agent.perceive_all_directions()
        print(f"All direction strengths: {all_strengths}")
        
        # Verify all directions are present
        expected_directions = {'w', 'a', 's', 'd'}
        if set(all_strengths.keys()) == expected_directions:
            print("✓ All directions perception works")
        else:
            print(f"✗ Missing directions. Expected: {expected_directions}, Got: {set(all_strengths.keys())}")
            return False
            
    except Exception as e:
        print(f"✗ All directions perception failed: {e}")
        return False
    
    print()
    
    # Test 3: Pheromone field data
    print("Test 3: Pheromone Field Data")
    print("-" * 40)
    
    try:
        field_data = agent.get_pheromone_field_data()
        required_keys = {
            'position', 'current_obstacle_pheromone', 'current_target_pheromone',
            'current_combined_pheromone', 'directional_strengths', 'gradients', 'timestamp'
        }
        
        if set(field_data.keys()) >= required_keys:
            print("✓ Pheromone field data contains all required keys")
            print(f"  Position: {field_data['position']}")
            print(f"  Current combined pheromone: {field_data['current_combined_pheromone']:.3f}")
            print(f"  Timestamp: {field_data['timestamp']}")
        else:
            missing_keys = required_keys - set(field_data.keys())
            print(f"✗ Missing keys in field data: {missing_keys}")
            return False
            
    except Exception as e:
        print(f"✗ Pheromone field data failed: {e}")
        return False
    
    print()
    
    # Test 4: Cached perception
    print("Test 4: Cached Perception")
    print("-" * 40)
    
    try:
        # Perform perception to populate cache
        agent.perceive_all_directions()
        
        # Get cached data
        cached_data = agent.get_cached_pheromone_perception()
        
        if len(cached_data) > 0:
            print("✓ Perception cache works")
            print(f"  Cached entries: {len(cached_data)}")
            
            # Test cache clearing
            agent.clear_pheromone_perception_cache()
            cleared_cache = agent.get_cached_pheromone_perception()
            
            if len(cleared_cache) == 0:
                print("✓ Cache clearing works")
            else:
                print(f"✗ Cache not properly cleared. Still has {len(cleared_cache)} entries")
                return False
        else:
            print("✗ Perception cache is empty after perception")
            return False
            
    except Exception as e:
        print(f"✗ Cached perception failed: {e}")
        return False
    
    print()
    
    # Test 5: Formatted output
    print("Test 5: Formatted Output")
    print("-" * 40)
    
    try:
        # Re-populate perception data
        agent.perceive_all_directions()
        
        formatted_output = agent.format_pheromone_perception(include_gradients=True)
        
        if len(formatted_output) > 0 and "Pheromone Perception" in formatted_output:
            print("✓ Formatted output generation works")
            print("\nSample formatted output:")
            print(formatted_output)
        else:
            print("✗ Formatted output is invalid or empty")
            return False
            
    except Exception as e:
        print(f"✗ Formatted output failed: {e}")
        return False
    
    print()
    
    # Test 6: Direction finding methods
    print("Test 6: Direction Finding Methods")
    print("-" * 40)
    
    try:
        # Test strongest direction
        strongest_dir, strongest_val = agent.get_strongest_pheromone_direction()
        print(f"Strongest pheromone direction: {strongest_dir} ({strongest_val:.3f})")
        
        # Test steepest gradient
        steepest_dir, steepest_val = agent.get_steepest_gradient_direction()
        print(f"Steepest gradient direction: {steepest_dir} ({steepest_val:.3f})")
        
        if strongest_dir in ['w', 'a', 's', 'd'] and steepest_dir in ['w', 'a', 's', 'd']:
            print("✓ Direction finding methods work")
        else:
            print(f"✗ Invalid directions returned: {strongest_dir}, {steepest_dir}")
            return False
            
    except Exception as e:
        print(f"✗ Direction finding methods failed: {e}")
        return False
    
    print()
    
    # Test 7: Add some obstacle pheromones and test again
    print("Test 7: Testing with Obstacle Pheromones")
    print("-" * 40)
    
    try:
        # Add obstacle pheromone near the agent
        pheromone_field.add_obstacle_pheromone(2, 1)  # To the right of agent
        pheromone_field.update_field()
        
        # Get new perception data
        field_data_with_obstacle = agent.get_pheromone_field_data()
        
        print("After adding obstacle pheromone:")
        print(f"  Right direction strength: {field_data_with_obstacle['directional_strengths']['d']:.3f}")
        
        # Print the updated perception
        agent.print_pheromone_perception(include_gradients=False)
        
        print("✓ Obstacle pheromone integration works")
        
    except Exception as e:
        print(f"✗ Obstacle pheromone testing failed: {e}")
        return False
    
    print()
    print("=== All Pheromone Perception Tests Passed! ===")
    return True


def test_edge_cases():
    """Test edge cases and error handling."""
    
    print("\n=== Testing Edge Cases ===\n")
    
    # Create minimal test environment
    config = SimulationConfig(maze_width=3, maze_height=3, obstacle_ratio=0.1)
    maze = Maze(3, 3, config.obstacle_ratio)
    maze.set_start_position(1, 1)
    maze.set_target_position(2, 2)
    
    pheromone_field = PheromoneField(3, 3, config)
    agent = Agent(1, 1, maze, pheromone_field, config)
    
    # Test invalid direction
    try:
        agent.perceive_pheromone_strength('x')
        print("✗ Should have raised ValueError for invalid direction")
        return False
    except ValueError:
        print("✓ Invalid direction properly rejected")
    except Exception as e:
        print(f"✗ Unexpected exception for invalid direction: {e}")
        return False
    
    # Test perception at boundary
    try:
        # Move agent to boundary position
        agent.reset_position(0, 0)  # Corner position
        strengths = agent.perceive_all_directions()
        
        # Some directions should be walls (negative infinity)
        wall_count = sum(1 for s in strengths.values() if s == float('-inf'))
        if wall_count >= 2:  # Corner should have at least 2 walls
            print(f"✓ Boundary perception works (detected {wall_count} walls)")
        else:
            print(f"✗ Expected walls at boundary, but got: {strengths}")
            return False
            
    except Exception as e:
        print(f"✗ Boundary perception failed: {e}")
        return False
    
    print("\n=== Edge Case Tests Passed! ===")
    return True


if __name__ == "__main__":
    print("Starting Agent Pheromone Perception Tests (Task 5.2)")
    print("=" * 60)
    
    success = True
    
    try:
        success &= test_pheromone_perception()
        success &= test_edge_cases()
        
        if success:
            print(f"\n🎉 ALL TESTS PASSED! Task 5.2 implementation is working correctly.")
        else:
            print(f"\n❌ Some tests failed. Please check the implementation.")
            
    except Exception as e:
        print(f"\n💥 Unexpected error during testing: {e}")
        import traceback
        traceback.print_exc()
        success = False
    
    exit(0 if success else 1)
