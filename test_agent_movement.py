"""
测试脚本：验证Agent基础移动系统实现（任务5.1）

测试内容：
- Agent类的初始化和位置管理
- 四个方向的移动逻辑和边界检查
- 移动历史记录和碰撞处理
- 到达目标点的检测功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from pheromone_maze.maze import Maze
from pheromone_maze.pheromone_field import PheromoneField
from pheromone_maze.agent import Agent
from pheromone_maze.config import SimulationConfig
from pheromone_maze.exceptions import InvalidPositionException


def test_agent_initialization():
    """测试Agent初始化功能"""
    print("测试1: Agent初始化...")
    
    # 创建5x5简单迷宫用于测试，使用固定种子确保可预测性
    config = SimulationConfig(maze_width=5, maze_height=5, obstacle_ratio=0.2)
    maze = Maze(5, 5, 0.2, seed=42)  # 使用固定种子
    pheromone_field = PheromoneField(5, 5, config)
    
    # 手动确保(1,1)位置没有障碍物
    maze.grid[1, 1] = 0
    
    # 初始化Agent在(1,1)
    agent = Agent(1, 1, maze, pheromone_field, config)
    
    # 验证初始位置
    assert agent.get_position() == (1, 1)
    assert agent.get_move_count() == 0
    assert agent.get_successful_move_count() == 0
    assert agent.get_collision_count() == 0
    assert len(agent.get_move_history()) == 1
    assert not agent.is_at_target()  # 因为还没有设置目标
    
    print("✓ Agent初始化测试通过")


def test_basic_movement():
    """测试基础移动功能"""
    print("\n测试2: 基础移动功能...")
    
    config = SimulationConfig(maze_width=5, maze_height=5, obstacle_ratio=0.1)  # 最小障碍物比例
    maze = Maze(5, 5, 0.1, seed=42)  # 使用固定种子
    
    # 手动清除中心区域的障碍物用于测试
    for x in range(1, 4):
        for y in range(1, 4):
            maze.grid[y, x] = 0
    
    pheromone_field = PheromoneField(5, 5, config)
    
    # 在中心位置初始化Agent
    agent = Agent(2, 2, maze, pheromone_field, config)
    initial_pos = agent.get_position()
    
    # 测试向右移动
    success = agent.move_right()
    assert success == True
    assert agent.get_position() == (3, 2)
    assert agent.get_move_count() == 1
    assert agent.get_successful_move_count() == 1
    
    # 测试向下移动
    success = agent.move_down()
    assert success == True
    assert agent.get_position() == (3, 3)
    assert agent.get_move_count() == 2
    assert agent.get_successful_move_count() == 2
    
    # 测试向左移动
    success = agent.move_left()
    assert success == True
    assert agent.get_position() == (2, 3)
    
    # 测试向上移动
    success = agent.move_up()
    assert success == True
    assert agent.get_position() == (2, 2)  # 回到原位
    
    # 验证移动历史
    history = agent.get_move_history()
    expected_history = [(2, 2), (3, 2), (3, 3), (2, 3), (2, 2)]
    assert history == expected_history
    
    print("✓ 基础移动功能测试通过")


def test_boundary_collision():
    """测试边界碰撞处理"""
    print("\n测试3: 边界碰撞处理...")
    
    config = SimulationConfig(maze_width=5, maze_height=5, obstacle_ratio=0.1)
    maze = Maze(5, 5, 0.1, seed=42)  # 使用固定种子
    
    # 确保(0,0)位置没有障碍物
    maze.grid[0, 0] = 0
    
    pheromone_field = PheromoneField(5, 5, config)
    
    # 在左上角初始化Agent
    agent = Agent(0, 0, maze, pheromone_field, config)
    
    # 尝试向上移动（应该失败）
    success = agent.move_up()
    assert success == False
    assert agent.get_position() == (0, 0)  # 位置未改变
    assert agent.get_move_count() == 1
    assert agent.get_successful_move_count() == 0
    assert agent.get_collision_count() == 1
    
    # 尝试向左移动（应该失败）
    success = agent.move_left()
    assert success == False
    assert agent.get_position() == (0, 0)
    assert agent.get_collision_count() == 2
    
    # 尝试有效移动
    success = agent.move_right()
    assert success == True
    assert agent.get_position() == (1, 0)
    assert agent.get_successful_move_count() == 1
    
    print("✓ 边界碰撞处理测试通过")


def test_obstacle_collision():
    """测试障碍物碰撞处理"""
    print("\n测试4: 障碍物碰撞处理...")
    
    config = SimulationConfig(maze_width=5, maze_height=5, obstacle_ratio=0.1)
    maze = Maze(5, 5, 0.1, seed=42)  # 使用固定种子
    
    # 确保测试位置没有障碍物，然后手动添加障碍物
    # 注意：maze.grid[y, x] 格式
    maze.grid[1, 1] = 0  # 确保(1,1)没有障碍物 - Agent位置
    maze.grid[2, 1] = 1  # 在(1, 2)位置添加障碍物 - Agent向下移动的目标
    maze.grid[1, 2] = 0  # 确保(2, 1)没有障碍物 - Agent向右移动的目标
    
    pheromone_field = PheromoneField(5, 5, config)
    
    # 在(1, 1)初始化Agent
    agent = Agent(1, 1, maze, pheromone_field, config)
    
    # 尝试向下移动到障碍物（应该失败）
    success = agent.move_down()
    assert success == False
    assert agent.get_position() == (1, 1)  # 位置未改变
    assert agent.get_collision_count() == 1
    
    # 尝试向右移动（应该成功）
    success = agent.move_right()
    assert success == True
    assert agent.get_position() == (2, 1)
    
    print("✓ 障碍物碰撞处理测试通过")


def test_target_detection():
    """测试目标点检测功能"""
    print("\n测试5: 目标点检测功能...")
    
    config = SimulationConfig(maze_width=5, maze_height=5, obstacle_ratio=0.1)
    maze = Maze(5, 5, 0.1, seed=42)  # 使用固定种子
    
    # 确保测试路径没有障碍物
    # 注意：maze.grid[y, x] 格式
    maze.grid[3, 2] = 0  # (2,3)
    maze.grid[3, 3] = 0  # (3,3)
    
    # 设置目标点
    maze.set_target_position(3, 3)
    
    pheromone_field = PheromoneField(5, 5, config)
    
    # 在(2, 3)初始化Agent（距离目标一步）
    agent = Agent(2, 3, maze, pheromone_field, config)
    
    # 验证初始不在目标
    assert not agent.is_at_target()
    assert not agent.has_reached_target()
    assert agent.get_steps_at_target() == 0
    
    # 移动到目标点
    success = agent.move_right()
    assert success == True
    assert agent.get_position() == (3, 3)
    assert agent.is_at_target()
    assert agent.has_reached_target()
    assert agent.get_steps_at_target() == 1
    
    # 在目标点再移动一步
    success = agent.move_down()
    assert success == True
    assert not agent.is_at_target()
    assert agent.get_steps_at_target() == 0
    
    print("✓ 目标点检测功能测试通过")


def test_invalid_initialization():
    """测试非法初始化处理"""
    print("\n测试6: 非法初始化处理...")
    
    config = SimulationConfig(maze_width=5, maze_height=5, obstacle_ratio=0.1)
    maze = Maze(5, 5, 0.1, seed=42)  # 使用固定种子
    
    # 添加障碍物
    maze.grid[1][1] = 1
    
    pheromone_field = PheromoneField(5, 5, config)
    
    # 测试边界外初始化
    try:
        agent = Agent(-1, 0, maze, pheromone_field, config)
        assert False, "应该抛出InvalidPositionException"
    except InvalidPositionException:
        pass
    
    # 测试障碍物上初始化
    try:
        agent = Agent(1, 1, maze, pheromone_field, config)
        assert False, "应该抛出InvalidPositionException"
    except InvalidPositionException:
        pass
    
    print("✓ 非法初始化处理测试通过")


def test_agent_statistics():
    """测试Agent统计信息"""
    print("\n测试7: Agent统计信息...")
    
    config = SimulationConfig(maze_width=5, maze_height=5, obstacle_ratio=0.1)
    maze = Maze(5, 5, 0.1, seed=42)  # 使用固定种子
    
    # 清除测试路径 - 注意：maze.grid[y, x] 格式
    maze.grid[0, 0] = 0  # (0,0)
    maze.grid[1, 0] = 0  # (0,1)
    maze.grid[1, 1] = 0  # (1,1)
    maze.grid[4, 4] = 0  # (4,4)
    
    maze.set_target_position(4, 4)
    
    pheromone_field = PheromoneField(5, 5, config)
    agent = Agent(0, 0, maze, pheromone_field, config)
    
    # 进行一些移动
    agent.move_right()  # (0,0) → (1,0) 成功
    agent.move_down()   # (1,0) → (1,1) 成功  
    agent.move_up()     # (1,1) → (1,0) 成功
    agent.move_up()     # (1,0) → (1,-1) 向上到边界外（失败）
    
    stats = agent.get_agent_statistics()
    
    assert stats['current_position'] == (1, 0)  # 最终位置应该是(1,0)
    assert stats['target_position'] == (4, 4)
    assert stats['total_moves_attempted'] == 4  # 总共尝试了4次移动
    assert stats['successful_moves'] == 3       # 3次成功
    assert stats['collision_count'] == 1        # 1次碰撞
    assert stats['success_rate'] == 3/4         # 成功率3/4
    assert not stats['at_target']
    
    print("✓ Agent统计信息测试通过")


def run_all_tests():
    """运行所有测试"""
    print("开始测试Agent基础移动系统实现（任务5.1）")
    print("=" * 50)
    
    try:
        test_agent_initialization()
        test_basic_movement()
        test_boundary_collision()
        test_obstacle_collision()
        test_target_detection()
        test_invalid_initialization()
        test_agent_statistics()
        
        print("\n" + "=" * 50)
        print("✅ 所有测试通过！Agent基础移动系统实现正确。")
        print("\n任务5.1完成情况总结：")
        print("✓ Agent类的初始化和位置管理 - 已实现")
        print("✓ 四个方向的移动逻辑和边界检查 - 已实现")
        print("✓ 移动历史记录和碰撞处理 - 已实现")
        print("✓ 到达目标点的检测功能 - 已实现")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    run_all_tests()
