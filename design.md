# 设计文档

## 概述

本系统实现一个基于信息素梯度场的迷宫导航模拟器。系统采用面向对象设计，包含迷宫环境、Agent智能体、信息素场管理和可视化模块。核心思想是通过双重信息素机制（障碍物排斥场和目标点吸引场）指导Agent进行路径规划。

## 架构

### 系统架构图

```mermaid
graph TB
    A[主程序 main.py] --> B[迷宫环境 Maze]
    A --> C[Agent智能体 Agent]
    A --> D[可视化器 Visualizer]
    
    B --> E[信息素场管理器 PheromoneField]
    C --> E
    
    E --> F[障碍物信息素 ObstaclePheromone]
    E --> G[目标点信息素 TargetPheromone]
    
    D --> H[实时显示 RealTimeDisplay]
    D --> I[结果分析 ResultAnalyzer]
```

### 数据流

1. **初始化阶段**：创建迷宫 → 初始化信息素场 → 放置Agent和目标点
2. **运行阶段**：Agent感知信息素 → 决策移动方向 → 执行移动 → 更新信息素场 → 重复
3. **可视化阶段**：实时渲染迷宫状态、Agent位置和信息素分布

## 组件和接口

### 1. 迷宫环境 (Maze)

```python
class Maze:
    def __init__(self, width: int, height: int, obstacle_ratio: float = 0.3)
    def generate_maze(self) -> None
    def is_valid_position(self, x: int, y: int) -> bool
    def is_obstacle(self, x: int, y: int) -> bool
    def get_neighbors(self, x: int, y: int) -> List[Tuple[int, int]]
    def set_start_position(self, x: int, y: int) -> None
    def set_target_position(self, x: int, y: int) -> None
```

**职责**：
- 生成随机迷宫或加载预定义迷宫
- 提供位置有效性检查
- 管理起始点和目标点位置

### 2. 信息素场管理器 (PheromoneField)

```python
class PheromoneField:
    def __init__(self, width: int, height: int, decay_rate: float = 0.95, diffusion_rate: float = 0.1)
    def add_obstacle_pheromone(self, x: int, y: int, intensity: float) -> None
    def add_target_pheromone(self, x: int, y: int, intensity: float) -> None
    def update_field(self) -> None
    def get_field_strength(self, x: int, y: int) -> float
    def get_directional_strength(self, x: int, y: int, direction: str) -> float
    def diffuse_pheromones(self) -> None
    def decay_pheromones(self) -> None
```

**职责**：
- 管理两种类型的信息素（障碍物和目标点）
- 实现信息素的扩散和衰减
- 提供方向性信息素强度查询

### 3. Agent智能体 (Agent)

```python
class Agent:
    def __init__(self, x: int, y: int, maze: Maze, pheromone_field: PheromoneField)
    def perceive_pheromones(self) -> Dict[str, float]
    def decide_move(self) -> str
    def move(self, direction: str) -> bool
    def is_at_target(self) -> bool
    def get_position(self) -> Tuple[int, int]
    def get_move_history(self) -> List[Tuple[int, int]]
```

**职责**：
- 感知四个方向的信息素场强度
- 基于信息素场做出移动决策
- 执行移动并处理碰撞
- 记录移动历史

### 4. 可视化器 (Visualizer)

```python
class Visualizer:
    def __init__(self, maze: Maze, pheromone_field: PheromoneField)
    def render_frame(self, agent: Agent) -> None
    def show_pheromone_field(self) -> None
    def show_agent_path(self, agent: Agent) -> None
    def display_statistics(self, steps: int, success: bool) -> None
    def save_animation(self, filename: str) -> None
```

**职责**：
- 实时渲染迷宫、Agent和信息素场
- 显示导航统计信息
- 保存动画结果

## 数据模型

### 1. 坐标系统
- 使用标准笛卡尔坐标系，原点(0,0)在左上角
- x轴向右为正，y轴向下为正
- 方向映射：'w'(上)、's'(下)、'a'(左)、'd'(右)

### 2. 信息素数据结构

```python
@dataclass
class PheromoneData:
    obstacle_field: np.ndarray  # 障碍物信息素场（负值）
    target_field: np.ndarray    # 目标点信息素场（正值）
    combined_field: np.ndarray  # 叠加后的总信息素场
    
@dataclass
class AgentState:
    x: int
    y: int
    move_count: int
    path_history: List[Tuple[int, int]]
    last_pheromone_perception: Dict[str, float]
```

### 3. 配置参数

```python
@dataclass
class SimulationConfig:
    maze_width: int = 20
    maze_height: int = 20
    obstacle_ratio: float = 0.3
    pheromone_decay_rate: float = 0.95
    pheromone_diffusion_rate: float = 0.1
    obstacle_pheromone_intensity: float = -10.0
    target_pheromone_intensity: float = 15.0
    max_steps: int = 1000
    visualization_delay: float = 0.1
```

## 错误处理

### 1. 输入验证
- 迷宫尺寸必须大于3x3
- 障碍物比例必须在0.1-0.8之间
- 信息素参数必须为正数
- 起始点和目标点不能在障碍物上

### 2. 运行时错误处理
- Agent移动越界：保持在当前位置，记录碰撞
- 信息素场数值溢出：使用numpy的安全数学运算
- 可视化失败：降级到文本输出模式
- 内存不足：自动调整迷宫大小

### 3. 异常恢复机制
```python
class NavigationException(Exception):
    pass

class MazeGenerationException(NavigationException):
    pass

class PheromoneOverflowException(NavigationException):
    pass
```

## 测试策略

### 1. 单元测试
- **迷宫生成测试**：验证迷宫结构的有效性和连通性
- **信息素场测试**：验证扩散、衰减和叠加算法的正确性
- **Agent决策测试**：验证基于信息素的决策逻辑
- **坐标系统测试**：验证位置计算和边界检查

### 2. 集成测试
- **端到端导航测试**：在简单迷宫中验证Agent能否到达目标
- **信息素场演化测试**：验证长时间运行下信息素场的稳定性
- **可视化集成测试**：验证可视化组件与核心逻辑的同步

### 3. 性能测试
- **大规模迷宫测试**：测试100x100迷宫的性能表现
- **内存使用测试**：监控长时间运行的内存泄漏
- **实时性测试**：确保可视化帧率满足实时观察需求

### 4. 算法验证测试
- **收敛性测试**：验证Agent在有解迷宫中的收敛性
- **鲁棒性测试**：测试在无解迷宫中的行为
- **参数敏感性测试**：测试不同参数组合下的性能

## 实现细节

### 1. 信息素扩散算法
使用高斯核进行信息素扩散，确保梯度场的平滑性：

```python
def diffuse_pheromones(self):
    kernel = np.array([[0.05, 0.1, 0.05],
                       [0.1,  0.4, 0.1],
                       [0.05, 0.1, 0.05]])
    self.obstacle_field = scipy.ndimage.convolve(self.obstacle_field, kernel, mode='constant')
    self.target_field = scipy.ndimage.convolve(self.target_field, kernel, mode='constant')
```

### 2. Agent决策算法
使用softmax函数增加决策的随机性，避免局部最优：

```python
def decide_move(self) -> str:
    pheromone_strengths = self.perceive_pheromones()
    # 添加少量随机噪声避免完全确定性行为
    strengths_with_noise = {k: v + np.random.normal(0, 0.1) 
                           for k, v in pheromone_strengths.items()}
    return max(strengths_with_noise, key=strengths_with_noise.get)
```

### 3. 目标点信息素生成
目标点持续散发信息素，强度随距离衰减：

```python
def generate_target_pheromones(self):
    target_x, target_y = self.maze.target_position
    for x in range(self.width):
        for y in range(self.height):
            distance = np.sqrt((x - target_x)**2 + (y - target_y)**2)
            self.target_field[y, x] = self.target_intensity / (1 + distance)
```

这个设计确保了系统的模块化、可扩展性和可测试性，同时实现了你要求的核心功能：基于双重信息素梯度场的智能导航。