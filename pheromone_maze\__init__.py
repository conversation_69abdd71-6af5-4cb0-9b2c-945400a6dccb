"""
Pheromone-based maze navigation system.

A simulation system where agents navigate through mazes using pheromone gradient fields.
Agents leave negative pheromones when hitting obstacles and are attracted to positive
pheromones emitted by target points.
"""

__version__ = "1.0.0"
__author__ = "Pheromone Maze Navigation System"

from .config import SimulationConfig
from .exceptions import NavigationException, MazeGenerationException, PheromoneOverflowException
from .utils import Direction, get_direction_offset, get_opposite_direction, is_valid_coordinate

__all__ = [
    "SimulationConfig",
    "NavigationException", 
    "MazeGenerationException",
    "PheromoneOverflowException",
    "Direction",
    "get_direction_offset",
    "get_opposite_direction", 
    "is_valid_coordinate"
]