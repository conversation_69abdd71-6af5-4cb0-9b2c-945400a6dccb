"""
Configuration data classes for the pheromone maze navigation system.
"""

from dataclasses import dataclass
from typing import Tuple
import numpy as np


@dataclass
class SimulationConfig:
    """Configuration parameters for the maze navigation simulation."""
    
    # Maze parameters
    maze_width: int = 20
    maze_height: int = 20
    obstacle_ratio: float = 0.3
    
    # Pheromone parameters
    pheromone_decay_rate: float = 0.95
    pheromone_diffusion_rate: float = 0.1
    obstacle_pheromone_intensity: float = -10.0
    target_pheromone_intensity: float = 15.0
    
    # Simulation parameters
    max_steps: int = 1000
    visualization_delay: float = 0.1
    
    # Agent parameters
    decision_noise_std: float = 0.1
    
    def __post_init__(self):
        """Validate configuration parameters."""
        if self.maze_width < 3 or self.maze_height < 3:
            raise ValueError("Maze dimensions must be at least 3x3")
        
        if not (0.1 <= self.obstacle_ratio <= 0.8):
            raise ValueError("Obstacle ratio must be between 0.1 and 0.8")
        
        if self.pheromone_decay_rate <= 0 or self.pheromone_decay_rate > 1:
            raise ValueError("Pheromone decay rate must be between 0 and 1")
        
        if self.pheromone_diffusion_rate < 0 or self.pheromone_diffusion_rate > 1:
            raise ValueError("Pheromone diffusion rate must be between 0 and 1")
        
        if self.max_steps <= 0:
            raise ValueError("Max steps must be positive")
        
        if self.visualization_delay < 0:
            raise ValueError("Visualization delay must be non-negative")


@dataclass
class PheromoneData:
    """Data structure for storing pheromone field information."""
    
    obstacle_field: np.ndarray  # Obstacle pheromone field (negative values)
    target_field: np.ndarray    # Target pheromone field (positive values)
    combined_field: np.ndarray  # Combined pheromone field
    
    def __post_init__(self):
        """Validate pheromone data arrays."""
        if not (self.obstacle_field.shape == self.target_field.shape == self.combined_field.shape):
            raise ValueError("All pheromone field arrays must have the same shape")


@dataclass
class AgentState:
    """State information for an agent."""
    
    x: int
    y: int
    move_count: int
    path_history: list  # List[Tuple[int, int]]
    last_pheromone_perception: dict  # Dict[str, float]
    
    def get_position(self) -> Tuple[int, int]:
        """Get current position as a tuple."""
        return (self.x, self.y)
    
    def add_to_path(self, x: int, y: int) -> None:
        """Add a position to the path history."""
        self.path_history.append((x, y))