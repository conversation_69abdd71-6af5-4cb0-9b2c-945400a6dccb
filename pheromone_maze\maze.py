"""
Maze environment module for the pheromone-based navigation system.
"""

import numpy as np
import random
from typing import List, Tuple, Optional
from .config import SimulationConfig
from .utils import is_valid_coordinate, get_new_position, Direction
from .exceptions import MazeGenerationException, InvalidPositionException


class Maze:
    """
    Maze environment that provides the navigation space for agents.
    
    The maze consists of a 2D grid with obstacles, open spaces, start position,
    and target position. It supports maze generation and position validation.
    """
    
    def __init__(self, width: int, height: int, obstacle_ratio: float = 0.3, seed: Optional[int] = None):
        """
        Initialize the maze with given dimensions and obstacle ratio.
        
        Args:
            width: Width of the maze (must be >= 3)
            height: Height of the maze (must be >= 3)
            obstacle_ratio: Ratio of obstacles to total cells (0.1 to 0.8)
            seed: Random seed for reproducible maze generation
            
        Raises:
            ValueError: If dimensions are too small or obstacle_ratio is invalid
        """
        if width < 3 or height < 3:
            raise ValueError("Maze dimensions must be at least 3x3")
        
        if not (0.1 <= obstacle_ratio <= 0.8):
            raise ValueError("Obstacle ratio must be between 0.1 and 0.8")
        
        self.width = width
        self.height = height
        self.obstacle_ratio = obstacle_ratio
        
        # Set random seed if provided
        if seed is not None:
            random.seed(seed)
            np.random.seed(seed)
        
        # Initialize maze grid (0 = open space, 1 = obstacle)
        self.grid = np.zeros((height, width), dtype=int)
        
        # Position tracking
        self.start_position: Optional[Tuple[int, int]] = None
        self.target_position: Optional[Tuple[int, int]] = None
        
        # Generate the maze
        self.generate_maze()
    
    def generate_maze(self) -> None:
        """
        Generate a random maze with obstacles distributed according to obstacle_ratio.
        
        Ensures that start and target positions can be placed and that there's
        at least one path between them.
        
        Raises:
            MazeGenerationException: If unable to generate a valid maze
        """
        max_attempts = 10
        
        for attempt in range(max_attempts):
            try:
                # Reset grid
                self.grid.fill(0)
                
                # Calculate number of obstacles
                total_cells = self.width * self.height
                num_obstacles = int(total_cells * self.obstacle_ratio)
                
                # Randomly place obstacles
                obstacle_positions = []
                while len(obstacle_positions) < num_obstacles:
                    x = random.randint(0, self.width - 1)
                    y = random.randint(0, self.height - 1)
                    
                    if (x, y) not in obstacle_positions:
                        obstacle_positions.append((x, y))
                        self.grid[y, x] = 1
                
                # Try to place start and target positions
                self._place_start_and_target()
                
                # Verify connectivity
                if self._verify_path_exists():
                    return  # Successfully generated maze
                
            except (InvalidPositionException, MazeGenerationException):
                continue  # Try again
        
        raise MazeGenerationException(f"Failed to generate valid maze after {max_attempts} attempts")
    
    def _place_start_and_target(self) -> None:
        """
        Place start and target positions in open spaces.
        
        Raises:
            MazeGenerationException: If unable to find suitable positions
        """
        # Find all open positions
        open_positions = []
        for y in range(self.height):
            for x in range(self.width):
                if not self.is_obstacle(x, y):
                    open_positions.append((x, y))
        
        if len(open_positions) < 2:
            raise MazeGenerationException("Not enough open spaces for start and target positions")
        
        # Randomly select start and target positions
        selected_positions = random.sample(open_positions, 2)
        self.start_position = selected_positions[0]
        self.target_position = selected_positions[1]
    
    def _verify_path_exists(self) -> bool:
        """
        Verify that a path exists between start and target positions using BFS.
        
        Returns:
            True if path exists, False otherwise
        """
        if not self.start_position or not self.target_position:
            return False
        
        # BFS to find path
        queue = [self.start_position]
        visited = {self.start_position}
        
        while queue:
            current_x, current_y = queue.pop(0)
            
            if (current_x, current_y) == self.target_position:
                return True
            
            # Check all neighbors
            for neighbor_x, neighbor_y in self.get_neighbors(current_x, current_y):
                if (neighbor_x, neighbor_y) not in visited:
                    visited.add((neighbor_x, neighbor_y))
                    queue.append((neighbor_x, neighbor_y))
        
        return False
    
    def is_valid_position(self, x: int, y: int) -> bool:
        """
        Check if the given position is valid (within bounds).
        
        Args:
            x: X coordinate
            y: Y coordinate
            
        Returns:
            True if position is within maze bounds, False otherwise
        """
        return is_valid_coordinate(x, y, self.width, self.height)
    
    def is_obstacle(self, x: int, y: int) -> bool:
        """
        Check if the given position contains an obstacle.
        
        Args:
            x: X coordinate
            y: Y coordinate
            
        Returns:
            True if position contains an obstacle, False otherwise
            
        Raises:
            InvalidPositionException: If position is out of bounds
        """
        if not self.is_valid_position(x, y):
            raise InvalidPositionException(x, y)
        
        return self.grid[y, x] == 1
    
    def is_open_space(self, x: int, y: int) -> bool:
        """
        Check if the given position is an open space (not an obstacle).
        
        Args:
            x: X coordinate
            y: Y coordinate
            
        Returns:
            True if position is open space, False otherwise
            
        Raises:
            InvalidPositionException: If position is out of bounds
        """
        return self.is_valid_position(x, y) and not self.is_obstacle(x, y)
    
    def get_neighbors(self, x: int, y: int) -> List[Tuple[int, int]]:
        """
        Get all valid neighboring positions (4-connected) that are open spaces.
        
        Args:
            x: X coordinate
            y: Y coordinate
            
        Returns:
            List of (x, y) tuples representing valid open neighbors
            
        Raises:
            InvalidPositionException: If position is out of bounds
        """
        if not self.is_valid_position(x, y):
            raise InvalidPositionException(x, y)
        
        neighbors = []
        
        # Check all four directions
        for direction in Direction.all_directions():
            new_x, new_y = get_new_position(x, y, direction)
            
            if self.is_valid_position(new_x, new_y) and not self.is_obstacle(new_x, new_y):
                neighbors.append((new_x, new_y))
        
        return neighbors
    
    def set_start_position(self, x: int, y: int) -> None:
        """
        Set the start position for agents.
        
        Args:
            x: X coordinate
            y: Y coordinate
            
        Raises:
            InvalidPositionException: If position is invalid or contains obstacle
        """
        if not self.is_valid_position(x, y):
            raise InvalidPositionException(x, y, "Start position is out of bounds")
        
        if self.is_obstacle(x, y):
            raise InvalidPositionException(x, y, "Start position cannot be on an obstacle")
        
        self.start_position = (x, y)
    
    def set_target_position(self, x: int, y: int) -> None:
        """
        Set the target position for navigation.
        
        Args:
            x: X coordinate
            y: Y coordinate
            
        Raises:
            InvalidPositionException: If position is invalid or contains obstacle
        """
        if not self.is_valid_position(x, y):
            raise InvalidPositionException(x, y, "Target position is out of bounds")
        
        if self.is_obstacle(x, y):
            raise InvalidPositionException(x, y, "Target position cannot be on an obstacle")
        
        self.target_position = (x, y)
    
    def get_start_position(self) -> Optional[Tuple[int, int]]:
        """Get the current start position."""
        return self.start_position
    
    def get_target_position(self) -> Optional[Tuple[int, int]]:
        """Get the current target position."""
        return self.target_position
    
    def get_maze_info(self) -> dict:
        """
        Get comprehensive information about the maze.
        
        Returns:
            Dictionary containing maze statistics and properties
        """
        total_cells = self.width * self.height
        obstacle_count = np.sum(self.grid)
        open_count = total_cells - obstacle_count
        actual_obstacle_ratio = obstacle_count / total_cells
        
        return {
            'width': self.width,
            'height': self.height,
            'total_cells': total_cells,
            'obstacle_count': obstacle_count,
            'open_count': open_count,
            'obstacle_ratio': actual_obstacle_ratio,
            'start_position': self.start_position,
            'target_position': self.target_position,
            'has_path': self._verify_path_exists() if self.start_position and self.target_position else None
        }
    
    def __str__(self) -> str:
        """String representation of the maze for debugging."""
        result = []
        for y in range(self.height):
            row = []
            for x in range(self.width):
                if (x, y) == self.start_position:
                    row.append('S')
                elif (x, y) == self.target_position:
                    row.append('T')
                elif self.is_obstacle(x, y):
                    row.append('#')
                else:
                    row.append('.')
            result.append(''.join(row))
        return '\n'.join(result)